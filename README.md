<a href="https://warriorwhocodes.com"><img src="repo_images/header.jpg"></a>

<p align="center">
  <a href="https://ankushsinghgandhi.github.io">
    <img src="https://img.shields.io/badge/Website-3b5998?style=flat-square&logo=google-chrome&logoColor=white" />
  </a>
  <a href="http://twitter.com/ankushsgandhi">
    <img src="https://img.shields.io/badge/-Twitter-blue?style=flat-square&logo=twitter&logoColor=white" />
  </a>
   <a href="https://www.linkedin.com/in/ankush-singh-gandhi-2487771aa/">
    <img src="https://img.shields.io/badge/-LinkedIn-0e76a8?style=flat-square&logo=Linkedin&logoColor=white" />
  </a>
  <a href="https://dev.to/@ankushsinghgandhi">
    <img src="https://img.shields.io/badge/-Dev.to-grey?style=flat-square&logo=dev.to&logoColor=white"/>
  </a>
  <a href="https://stackoverflow.com/users/13790266/ankush-singh">
    <img src="https://img.shields.io/badge/-Stackoverflow-orange?style=flat-square&logo=stackoverflow&logoColor=white"/>
  </a>
  <a href="https://leetcode.com/ankushsinghgandhi/">
    <img src="https://img.shields.io/badge/-Leetcode-yellow?style=flat-square&logo=Leetcode&logoColor=white"/>
  </a>
    <a href="https://www.hackerrank.com/ankushsgandhi">
    <img src="https://img.shields.io/badge/-HackerRank-green?style=flat-square&logo=Hackerrank&logoColor=white"/>
  </a>
    <a href="https://www.hackerearth.com/@bhanusinghank">
    <img src="https://img.shields.io/badge/-Hackerearth-purple?style=flat-square&logo=Hackerearth&logoColor=white"/>
  </a>
</p>

# Super Trader Flutter

Super Trader App is a Flutter based application designed for trading stocks. It allows users to view real-time & historic stock prices, make purchases, and track their transaction history. Additionally, the admin panel provides features for managing user accounts, monitoring transactions, and more.

## Features

- **User Authentication:** Users can register, login, and securely manage their accounts.
- **Stock Trading:** Buy and sell stocks with real-time market data.
- **Portfolio Management:** Track the stocks you own, view purchase history, and analyze performance.
- **Watchlist:** Add and remove stocks into watchlist for quick access.
- **Push Notifications:** Receive alerts for price changes and important updates.
- **Offline Mode:** Access app features and view cached data even without an internet connection.

## Installation

1. **Clone the repository:**
   ```
   git clone https://github.com/AnkushSinghGandhi/super_trader_flutter.git
   ```

2. **Navigate to the project directory:**
   ```
   cd super_trader_flutter
   ```

3. **Install dependencies:**
   ```
   flutter pub get
   ```

4. **Run the app:**
   ```
   flutter run
   ```

5. **Build the app for production:**
   ```
   flutter build apk  # For Android
   flutter build ios  # For iOS
   ```

6. **Deploy the built APK or IPA file to your device** or emulator.

## Backend Server

This app requires a backend server to handle user authentication, stock data, and other functionalities. The backend server is built with Flask and MongoDB. 

### Installation

1. **Clone the backend repository:**
   ```
   git clone https://github.com/AnkushSinghGandhi/super_trader_flask.git
   ```

2. **Navigate to the backend directory:**
   ```
   cd super_trader_flask
   ```

3. **Install dependencies:**
   ```
   pip install -r requirements.txt
   ```

4. **Run the Flask server:**
   ```
   python app.py
   ```

5. **The backend server will start running** on `http://localhost:5000`.

## Usage

- Register a new account or login with existing credentials.
- Explore the stock market and add your favorite stocks to track.
- Buy and sell stocks based on real-time market data.
- Monitor your portfolio performance and transaction history.
- Receive push notifications for price changes and important updates.
- Use the app in offline mode to access features and view cached data.

## Contributing

Contributions are welcome! If you find any bugs or have suggestions for new features, please open an issue or submit a pull request.

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

## Acknowledgements

- **Flutter:** [https://flutter.dev/](https://flutter.dev/)
- **Firebase:** [https://firebase.google.com/](https://firebase.google.com/)

## Support

For any questions or assistance, feel free to contact the project maintainer at [<EMAIL>](mailto:<EMAIL>).

Thank you for using Super Trader Flutter! Happy trading!
